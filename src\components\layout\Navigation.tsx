import React from 'react';
import { Navigation as PolarisNavigation } from '@shopify/polaris';
import { 
  HomeIcon,
  NotificationIcon,
  EmailIcon,
  ChatIcon,
  PhoneIcon,
  SettingsIcon,
  CreditCardIcon,
  AutomationIcon
} from '@shopify/polaris-icons';

interface NavigationProps {
  currentPath: string;
  onNavigate: (path: string) => void;
}

const Navigation: React.FC<NavigationProps> = ({ currentPath, onNavigate }) => {
  const navigationItems = [
    {
      url: '/',
      label: 'Dashboard',
      icon: HomeIcon,
      selected: currentPath === '/',
    },
    {
      url: '/notifications',
      label: 'Notifications',
      icon: NotificationIcon,
      selected: currentPath === '/notifications',
      subNavigationItems: [
        {
          url: '/notifications/whatsapp',
          label: 'WhatsApp',
          selected: currentPath === '/notifications/whatsapp',
        },
        {
          url: '/notifications/email',
          label: 'Email',
          selected: currentPath === '/notifications/email',
        },
        {
          url: '/notifications/sms',
          label: 'SMS',
          selected: currentPath === '/notifications/sms',
        },
      ],
    },
    {
      url: '/campaigns',
      label: 'Campaign Flows',
      icon: AutomationIcon,
      selected: currentPath === '/campaigns',
    },
    {
      url: '/templates',
      label: 'Templates',
      icon: ChatIcon,
      selected: currentPath === '/templates',
    },
    {
      url: '/settings',
      label: 'Settings',
      icon: SettingsIcon,
      selected: currentPath === '/settings',
    },
    {
      url: '/subscription',
      label: 'Subscription',
      icon: CreditCardIcon,
      selected: currentPath === '/subscription',
    },
  ];

  return (
    <PolarisNavigation location={currentPath}>
      <PolarisNavigation.Section
        items={navigationItems.map(item => ({
          ...item,
          onClick: () => onNavigate(item.url),
          subNavigationItems: item.subNavigationItems?.map(subItem => ({
            ...subItem,
            onClick: () => onNavigate(subItem.url),
          })),
        }))}
      />
    </PolarisNavigation>
  );
};

export default Navigation;