import React, { useState } from 'react';
import { Page, Layout, Card, Text, <PERSON><PERSON>, Ta<PERSON>, Banner, ProgressBar, Badge } from '@shopify/polaris';

interface NotificationsProps {
  onNavigate: (path: string) => void;
}

const Notifications: React.FC<NotificationsProps> = ({ onNavigate }) => {
  const [selectedTab, setSelectedTab] = useState(0);

  const tabs = [
    { id: 'overview', content: 'Overview' },
    { id: 'whatsapp', content: 'WhatsApp' },
    { id: 'email', content: 'Email' },
    { id: 'sms', content: 'SMS' },
  ];

  const recentNotifications = [
    {
      id: 1,
      type: 'WhatsApp',
      message: 'iPhone Case Offer',
      recipients: 245,
      status: 'delivered',
      sentAt: '2 hours ago'
    },
    {
      id: 2,
      type: 'Email',
      message: 'Order Follow-up',
      recipients: 128,
      status: 'delivered', 
      sentAt: '5 hours ago'
    },
    {
      id: 3,
      type: 'WhatsApp',
      message: 'Birthday Discount',
      recipients: 89,
      status: 'pending',
      sentAt: '1 day ago'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'delivered':
        return <Badge tone="success">Delivered</Badge>;
      case 'pending':
        return <Badge tone="attention">Pending</Badge>;
      case 'failed':
        return <Badge tone="critical">Failed</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  const renderOverview = () => (
    <Layout>
      <Layout.Section>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <div className="p-4">
              <Text variant="headingMd" as="h3">Messages This Month</Text>
              <Text variant="headingXl" as="p">1,247</Text>
              <Text variant="bodySm" as="p" tone="subdued">+12% from last month</Text>
              <div className="mt-2">
                <ProgressBar progress={62} size="small" />
                <Text variant="bodySm" as="p" tone="subdued">62% of monthly limit</Text>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="p-4">
              <Text variant="headingMd" as="h3">Active Templates</Text>
              <Text variant="headingXl" as="p">24</Text>
              <Text variant="bodySm" as="p" tone="subdued">8 WhatsApp, 10 Email, 6 SMS</Text>
            </div>
          </Card>
          
          <Card>
            <div className="p-4">
              <Text variant="headingMd" as="h3">Delivery Rate</Text>
              <Text variant="headingXl" as="p">98.2%</Text>
              <Text variant="bodySm" as="p" tone="success">Excellent performance</Text>
            </div>
          </Card>
        </div>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <Text variant="headingMd" as="h3">Recent Notifications</Text>
              <Button onClick={() => onNavigate('/templates')}>Send New</Button>
            </div>
            
            <div className="space-y-4">
              {recentNotifications.map((notification) => (
                <div key={notification.id} className="flex justify-between items-center p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Text variant="bodyMd" as="span" fontWeight="semibold">
                        {notification.message}
                      </Text>
                      <Badge>{notification.type}</Badge>
                    </div>
                    <Text variant="bodySm" as="p" tone="subdued">
                      {notification.recipients} recipients • {notification.sentAt}
                    </Text>
                  </div>
                  <div className="flex items-center gap-3">
                    {getStatusBadge(notification.status)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );

  const renderWhatsApp = () => (
    <Layout>
      <Layout.Section>
        <Banner tone="info">
          <p>Connect your WhatsApp Business account to start sending messages. Templates must be approved by Meta before use.</p>
        </Banner>
      </Layout.Section>
      
      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">WhatsApp Business Setup</Text>
            <div className="mt-4 space-y-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Connect Meta Business Account</Text>
                  <Text variant="bodySm" as="p" tone="subdued">Link your Facebook Business Manager</Text>
                </div>
                <Button variant="primary">Connect</Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Phone Number Verification</Text>
                  <Text variant="bodySm" as="p" tone="subdued">Verify your business phone number</Text>
                </div>
                <Badge tone="attention">Pending</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Business Profile</Text>
                  <Text variant="bodySm" as="p" tone="subdued">Complete your business information</Text>
                </div>
                <Badge tone="attention">Pending</Badge>
              </div>
            </div>
          </div>
        </Card>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <Text variant="headingMd" as="h3">Message Templates</Text>
              <Button onClick={() => onNavigate('/templates')}>Create Template</Button>
            </div>
            <Text variant="bodyMd" as="p" tone="subdued">
              No templates created yet. Create your first WhatsApp message template to get started.
            </Text>
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );

  const renderEmail = () => (
    <Layout>
      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">SMTP Configuration</Text>
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium">SMTP Server</label>
                  <input className="w-full mt-1 p-2 border rounded" placeholder="smtp.gmail.com" />
                </div>
                <div>
                  <label className="block text-sm font-medium">Port</label>
                  <input className="w-full mt-1 p-2 border rounded" placeholder="587" />
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium">Username</label>
                  <input className="w-full mt-1 p-2 border rounded" placeholder="<EMAIL>" />
                </div>
                <div>
                  <label className="block text-sm font-medium">Password</label>
                  <input type="password" className="w-full mt-1 p-2 border rounded" placeholder="App password" />
                </div>
              </div>
            </div>
            <div className="mt-4 flex gap-2">
              <Button variant="primary">Save Configuration</Button>
              <Button>Test Connection</Button>
            </div>
          </div>
        </Card>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <Text variant="headingMd" as="h3">Email Templates</Text>
              <Button onClick={() => onNavigate('/templates')}>Create Template</Button>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 border rounded-lg">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Order Confirmation</Text>
                  <Text variant="bodySm" as="p" tone="subdued">Sent automatically after purchase</Text>
                </div>
                <Badge tone="success">Active</Badge>
              </div>
              <div className="flex justify-between items-center p-3 border rounded-lg">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Shipping Update</Text>
                  <Text variant="bodySm" as="p" tone="subdued">Tracking information email</Text>
                </div>
                <Badge tone="success">Active</Badge>
              </div>
            </div>
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );

  const renderSMS = () => (
    <Layout>
      <Layout.Section>
        <Banner tone="warning">
          <p>SMS feature requires a paid subscription plan. Upgrade to Basic or Advanced to enable SMS notifications.</p>
        </Banner>
      </Layout.Section>
      
      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">SMS Provider Setup</Text>
            <div className="mt-4">
              <Text variant="bodyMd" as="p" tone="subdued">
                Choose your SMS provider and configure your account credentials.
              </Text>
              
              <div className="mt-4 space-y-3">
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <Text variant="bodyMd" as="p" fontWeight="semibold">Twilio</Text>
                      <Text variant="bodySm" as="p" tone="subdued">Global SMS delivery with high reliability</Text>
                    </div>
                    <Button disabled>Configure</Button>
                  </div>
                </div>
                
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <Text variant="bodyMd" as="p" fontWeight="semibold">Plivo</Text>
                      <Text variant="bodySm" as="p" tone="subdued">Cost-effective SMS solution</Text>
                    </div>
                    <Button disabled>Configure</Button>
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <Button variant="primary" onClick={() => onNavigate('/subscription')}>
                  Upgrade to Enable SMS
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0: return renderOverview();
      case 1: return renderWhatsApp();
      case 2: return renderEmail();
      case 3: return renderSMS();
      default: return renderOverview();
    }
  };

  return (
    <Page title="Notifications" subtitle="Manage your communication channels">
      <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
        {renderTabContent()}
      </Tabs>
    </Page>
  );
};

export default Notifications;