import React, { useState } from 'react';
import { Page, Layout, Card, Text, Button, TextField, Checkbox, Banner, Tabs } from '@shopify/polaris';
import { SettingsIcon, SaveIcon, ConnectIcon } from '@shopify/polaris-icons';

interface SettingsProps {
  onNavigate: (path: string) => void;
}

const Settings: React.FC<SettingsProps> = ({ onNavigate }) => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [shopifyWebhookUrl, setShopifyWebhookUrl] = useState('');
  const [autoOptIn, setAutoOptIn] = useState(true);
  const [gdprCompliance, setGdprCompliance] = useState(true);
  const [notificationSettings, setNotificationSettings] = useState({
    orderCreated: true,
    orderFulfilled: true,
    orderCancelled: true,
    customerCreated: false,
  });

  const tabs = [
    { id: 'general', content: 'General' },
    { id: 'shopify', content: 'Shopify Integration' },
    { id: 'notifications', content: 'Notifications' },
    { id: 'compliance', content: 'Compliance' },
  ];

  const handleSaveSettings = () => {
    console.log('Saving settings...');
    // Implementation for saving settings
  };

  const renderGeneral = () => (
    <Layout>
      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">Business Information</Text>
            <div className="mt-4 space-y-4">
              <TextField
                label="Business Name"
                value="My Shopify Store"
                onChange={() => {}}
                autoComplete="organization"
              />
              <TextField
                label="Contact Email"
                value="<EMAIL>"
                onChange={() => {}}
                type="email"
                autoComplete="email"
              />
              <TextField
                label="Phone Number"
                value="+****************"
                onChange={() => {}}
                type="tel"
                autoComplete="tel"
              />
              <TextField
                label="Business Address"
                value="123 Main St, City, State 12345"
                onChange={() => {}}
                multiline={3}
                autoComplete="street-address"
              />
            </div>
          </div>
        </Card>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">App Preferences</Text>
            <div className="mt-4 space-y-4">
              <Checkbox
                label="Enable automatic customer opt-in for notifications"
                checked={autoOptIn}
                onChange={setAutoOptIn}
                helpText="New customers will be automatically subscribed to notifications"
              />
              <Checkbox
                label="Send daily activity reports"
                checked={true}
                onChange={() => {}}
                helpText="Receive daily summaries of campaign performance"
              />
              <Checkbox
                label="Enable debug logging"
                checked={false}
                onChange={() => {}}
                helpText="Keep detailed logs for troubleshooting (may affect performance)"
              />
            </div>
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );

  const renderShopify = () => (
    <Layout>
      <Layout.Section>
        <Banner tone="info">
          <p>Connect your Shopify store to automatically sync customers, orders, and products.</p>
        </Banner>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">Shopify Connection</Text>
            <div className="mt-4">
              <div className="flex items-center justify-between p-4 border rounded-lg bg-green-50">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Store Connected</Text>
                  <Text variant="bodySm" as="p" tone="subdued">my-store.myshopify.com</Text>
                </div>
                <Button icon={ConnectIcon} disabled>Connected</Button>
              </div>
            </div>
          </div>
        </Card>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">Webhook Configuration</Text>
            <div className="mt-4 space-y-4">
              <TextField
                label="Webhook URL"
                value={shopifyWebhookUrl}
                onChange={setShopifyWebhookUrl}
                placeholder="https://your-app.com/webhooks/shopify"
                helpText="URL where Shopify will send event notifications"
                autoComplete="url"
              />
              
              <Text variant="bodyMd" as="p" fontWeight="semibold">Active Webhooks:</Text>
              <div className="space-y-2">
                {[
                  'orders/create',
                  'orders/updated', 
                  'orders/fulfilled',
                  'orders/cancelled',
                  'customers/create'
                ].map((webhook) => (
                  <div key={webhook} className="flex items-center justify-between p-2 border rounded">
                    <Text variant="bodySm" as="span">{webhook}</Text>
                    <Text variant="bodySm" as="span" tone="success">Active</Text>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">Data Sync</Text>
            <div className="mt-4 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Customer Data</Text>
                  <Text variant="bodySm" as="p" tone="subdued">Last synced: 2 hours ago</Text>
                </div>
                <Button>Sync Now</Button>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Product Catalog</Text>
                  <Text variant="bodySm" as="p" tone="subdued">Last synced: 5 hours ago</Text>
                </div>
                <Button>Sync Now</Button>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">Order History</Text>
                  <Text variant="bodySm" as="p" tone="subdued">Last synced: 1 hour ago</Text>
                </div>
                <Button>Sync Now</Button>
              </div>
            </div>
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );

  const renderNotifications = () => (
    <Layout>
      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">Automatic Notifications</Text>
            <Text variant="bodySm" as="p" tone="subdued">
              Configure which events should trigger automatic notifications to customers
            </Text>
            
            <div className="mt-4 space-y-4">
              <Checkbox
                label="Order Created"
                checked={notificationSettings.orderCreated}
                onChange={(checked) => setNotificationSettings(prev => ({ ...prev, orderCreated: checked }))}
                helpText="Send confirmation when customer places an order"
              />
              
              <Checkbox
                label="Order Fulfilled"
                checked={notificationSettings.orderFulfilled}
                onChange={(checked) => setNotificationSettings(prev => ({ ...prev, orderFulfilled: checked }))}
                helpText="Send shipping notification when order is fulfilled"
              />
              
              <Checkbox
                label="Order Cancelled"
                checked={notificationSettings.orderCancelled}
                onChange={(checked) => setNotificationSettings(prev => ({ ...prev, orderCancelled: checked }))}
                helpText="Send cancellation notification to customer"
              />
              
              <Checkbox
                label="Customer Registration"
                checked={notificationSettings.customerCreated}
                onChange={(checked) => setNotificationSettings(prev => ({ ...prev, customerCreated: checked }))}
                helpText="Send welcome message to new customers"
              />
            </div>
          </div>
        </Card>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">Notification Timing</Text>
            <div className="mt-4 space-y-4">
              <TextField
                label="Order Confirmation Delay"
                value="0"
                onChange={() => {}}
                suffix="minutes"
                helpText="Delay before sending order confirmation"
                type="number"
                autoComplete="off"
              />
              
              <TextField
                label="Fulfillment Notification Delay"
                value="30"
                onChange={() => {}}
                suffix="minutes"
                helpText="Delay before sending shipping notification"
                type="number"
                autoComplete="off"
              />
              
              <TextField
                label="Follow-up Campaign Delay"
                value="7"
                onChange={() => {}}
                suffix="days"
                helpText="Default delay for follow-up campaigns"
                type="number"
                autoComplete="off"
              />
            </div>
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );

  const renderCompliance = () => (
    <Layout>
      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">GDPR & Privacy</Text>
            <div className="mt-4 space-y-4">
              <Checkbox
                label="Enable GDPR compliance features"
                checked={gdprCompliance}
                onChange={setGdprCompliance}
                helpText="Include unsubscribe links and privacy notices in all communications"
              />
              
              <Checkbox
                label="Require explicit consent for marketing messages"
                checked={true}
                onChange={() => {}}
                helpText="Customers must explicitly opt-in to receive promotional content"
              />
              
              <Checkbox
                label="Automatically delete customer data on request"
                checked={false}
                onChange={() => {}}
                helpText="Honor data deletion requests within 30 days"
              />
              
              <TextField
                label="Privacy Policy URL"
                value="https://mystore.com/privacy"
                onChange={() => {}}
                helpText="Link to your privacy policy (included in communications)"
                autoComplete="url"
              />
              
              <TextField
                label="Terms of Service URL"
                value="https://mystore.com/terms"
                onChange={() => {}}
                helpText="Link to your terms of service"
                autoComplete="url"
              />
            </div>
          </div>
        </Card>
      </Layout.Section>

      <Layout.Section>
        <Card>
          <div className="p-4">
            <Text variant="headingMd" as="h3">Opt-out Management</Text>
            <div className="mt-4 space-y-4">
              <TextField
                label="Unsubscribe Page URL"
                value="https://mystore.com/unsubscribe"
                onChange={() => {}}
                helpText="Custom unsubscribe page (optional)"
                autoComplete="url"
              />
              
              <TextField
                label="Opt-out Keywords"
                value="STOP, UNSUBSCRIBE, QUIT"
                onChange={() => {}}
                helpText="Keywords that trigger automatic unsubscribe (for SMS)"
                multiline={2}
                autoComplete="off"
              />
              
              <Text variant="bodySm" as="p" tone="subdued">
                <strong>Legal Notice:</strong> All promotional messages will include required opt-out instructions 
                and sender identification as per local regulations.
              </Text>
            </div>
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0: return renderGeneral();
      case 1: return renderShopify();
      case 2: return renderNotifications();
      case 3: return renderCompliance();
      default: return renderGeneral();
    }
  };

  return (
    <Page 
      title="Settings" 
      subtitle="Configure your app preferences and integrations"
      primaryAction={{
        content: 'Save Changes',
        icon: SaveIcon,
        onAction: handleSaveSettings
      }}
    >
      <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
        {renderTabContent()}
      </Tabs>
    </Page>
  );
};

export default Settings;