import React from 'react';
import { AppProvider as PolarisAppProvider } from '@shopify/polaris';
import '@shopify/polaris/build/esm/styles.css';

interface AppProviderProps {
  children: React.ReactNode;
}

const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  return (
    <PolarisAppProvider
      i18n={{}}
      theme="light"
      features={{
        newDesignLanguage: true,
      }}
    >
      {children}
    </PolarisAppProvider>
  );
};

export default AppProvider;