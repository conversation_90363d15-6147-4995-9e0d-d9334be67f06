import React, { useState } from 'react';
import { Page, Layout, Card, Text, Button, Badge, Modal, TextField, Select } from '@shopify/polaris';
import { PlusIcon, AutomationIcon, PlayIcon, ClockIcon, EditIcon } from '@shopify/polaris-icons';

interface CampaignsProps {
  onNavigate: (path: string) => void;
}

const Campaigns: React.FC<CampaignsProps> = ({ onNavigate }) => {
  const [modalActive, setModalActive] = useState(false);
  const [campaignName, setCampaignName] = useState('');
  const [triggerType, setTriggerType] = useState('purchase');

  const campaigns = [
    {
      id: 1,
      name: 'iPhone Case Upsell',
      status: 'active',
      trigger: 'Purchase: iPhone',
      channel: 'WhatsApp',
      delay: '1 week',
      recipients: 245,
      conversions: 23,
      revenue: '$1,150'
    },
    {
      id: 2,
      name: 'Birthday Discount',
      status: 'active',
      trigger: 'Customer Birthday',
      channel: 'Email',
      delay: 'On birthday',
      recipients: 89,
      conversions: 12,
      revenue: '$890'
    },
    {
      id: 3,
      name: 'Abandoned Cart Recovery',
      status: 'paused',
      trigger: 'Cart Abandonment',
      channel: 'WhatsApp',
      delay: '24 hours',
      recipients: 156,
      conversions: 8,
      revenue: '$420'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge tone="success">Active</Badge>;
      case 'paused':
        return <Badge tone="attention">Paused</Badge>;
      case 'draft':
        return <Badge>Draft</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  const handleCreateCampaign = () => {
    console.log('Creating campaign:', { campaignName, triggerType });
    setModalActive(false);
    setCampaignName('');
    setTriggerType('purchase');
  };

  const triggerOptions = [
    { label: 'Product Purchase', value: 'purchase' },
    { label: 'Order Status Change', value: 'order_status' },
    { label: 'Customer Birthday', value: 'birthday' },
    { label: 'Cart Abandonment', value: 'cart_abandon' },
    { label: 'Customer Registration', value: 'registration' },
  ];

  return (
    <Page 
      title="Campaign Flows" 
      subtitle="Create and manage automated marketing campaigns"
      primaryAction={{
        content: 'Create Campaign',
        icon: PlusIcon,
        onAction: () => setModalActive(true)
      }}
    >
      <Layout>
        {/* Campaign Stats */}
        <Layout.Section>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <div className="p-4">
                <Text variant="headingMd" as="h3">Active Campaigns</Text>
                <Text variant="headingXl" as="p">8</Text>
                <Text variant="bodySm" as="p" tone="success">2 added this week</Text>
              </div>
            </Card>
            
            <Card>
              <div className="p-4">
                <Text variant="headingMd" as="h3">Total Recipients</Text>
                <Text variant="headingXl" as="p">490</Text>
                <Text variant="bodySm" as="p" tone="subdued">This month</Text>
              </div>
            </Card>
            
            <Card>
              <div className="p-4">
                <Text variant="headingMd" as="h3">Conversion Rate</Text>
                <Text variant="headingXl" as="p">8.8%</Text>
                <Text variant="bodySm" as="p" tone="success">+2.3% from last month</Text>
              </div>
            </Card>
            
            <Card>
              <div className="p-4">
                <Text variant="headingMd" as="h3">Revenue Generated</Text>
                <Text variant="headingXl" as="p">$2,460</Text>
                <Text variant="bodySm" as="p" tone="success">ROI: 320%</Text>
              </div>
            </Card>
          </div>
        </Layout.Section>

        {/* Campaign List */}
        <Layout.Section>
          <Card>
            <div className="p-4">
              <div className="flex justify-between items-center mb-4">
                <Text variant="headingMd" as="h3">Active Campaigns</Text>
                <Button variant="plain" onClick={() => onNavigate('/templates')}>
                  Manage Templates
                </Button>
              </div>
              
              <div className="space-y-4">
                {campaigns.map((campaign) => (
                  <div key={campaign.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <Text variant="bodyLg" as="h4" fontWeight="semibold">
                            {campaign.name}
                          </Text>
                          {getStatusBadge(campaign.status)}
                          <Badge>{campaign.channel}</Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <Text variant="bodySm" as="p" tone="subdued">Trigger</Text>
                            <Text variant="bodySm" as="p">{campaign.trigger}</Text>
                          </div>
                          <div>
                            <Text variant="bodySm" as="p" tone="subdued">Delay</Text>
                            <Text variant="bodySm" as="p">{campaign.delay}</Text>
                          </div>
                          <div>
                            <Text variant="bodySm" as="p" tone="subdued">Recipients</Text>
                            <Text variant="bodySm" as="p">{campaign.recipients}</Text>
                          </div>
                          <div>
                            <Text variant="bodySm" as="p" tone="subdued">Conversions</Text>
                            <Text variant="bodySm" as="p">{campaign.conversions} ({campaign.revenue})</Text>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        <Button size="slim" icon={EditIcon}>
                          Edit
                        </Button>
                        {campaign.status === 'active' ? (
                          <Button size="slim" icon={ClockIcon}>
                            Pause
                          </Button>
                        ) : (
                          <Button size="slim" icon={PlayIcon}>
                            Resume
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 rounded p-3 mt-3">
                      <Text variant="bodySm" as="p" tone="subdued">
                        Flow: {campaign.trigger} → Wait {campaign.delay} → Send {campaign.channel} message → Track conversion
                      </Text>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Layout.Section>

        {/* Campaign Builder Preview */}
        <Layout.Section>
          <Card>
            <div className="p-4">
              <Text variant="headingMd" as="h3">Campaign Flow Builder</Text>
              <div className="mt-4 p-6 border-2 border-dashed border-gray-300 rounded-lg text-center">
                <AutomationIcon className="mx-auto mb-3 h-12 w-12 text-gray-400" />
                <Text variant="bodyMd" as="p" tone="subdued">
                  Visual flow builder coming soon! Create complex automation workflows with drag-and-drop interface.
                </Text>
                <div className="mt-4">
                  <Button onClick={() => setModalActive(true)}>
                    Create Your First Flow
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </Layout.Section>
      </Layout>

      {/* Create Campaign Modal */}
      <Modal
        open={modalActive}
        onClose={() => setModalActive(false)}
        title="Create New Campaign"
        primaryAction={{
          content: 'Create Campaign',
          onAction: handleCreateCampaign,
          disabled: !campaignName.trim()
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => setModalActive(false)
          }
        ]}
      >
        <Modal.Section>
          <div className="space-y-4">
            <TextField
              label="Campaign Name"
              value={campaignName}
              onChange={setCampaignName}
              placeholder="e.g., iPhone Case Upsell"
              autoComplete="off"
            />
            
            <Select
              label="Trigger Event"
              options={triggerOptions}
              value={triggerType}
              onChange={setTriggerType}
            />
            
            <div className="p-4 bg-blue-50 rounded">
              <Text variant="bodySm" as="p">
                💡 <strong>Pro Tip:</strong> Start with simple triggers like product purchases. 
                You can add complex conditions and multiple steps after creating the campaign.
              </Text>
            </div>
          </div>
        </Modal.Section>
      </Modal>
    </Page>
  );
};

export default Campaigns;