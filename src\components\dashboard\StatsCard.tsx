import React from 'react';
import { Card, Text, ButtonGroup, Button } from '@shopify/polaris';

interface StatsCardProps {
  title: string;
  value: string | number;
  trend?: {
    direction: 'up' | 'down';
    percentage: number;
  };
  color?: 'success' | 'warning' | 'critical' | 'info';
  action?: {
    label: string;
    onClick: () => void;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({ 
  title, 
  value, 
  trend, 
  color = 'info',
  action 
}) => {
  const trendColor = trend?.direction === 'up' ? 'success' : 'critical';
  
  return (
    <Card>
      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <Text variant="bodyMd" as="span" tone="subdued">
            {title}
          </Text>
          {trend && (
            <Text variant="bodySm" as="span" tone={trendColor}>
              {trend.direction === 'up' ? '↗' : '↘'} {trend.percentage}%
            </Text>
          )}
        </div>
        
        <Text variant="headingLg" as="h3">
          {value}
        </Text>
        
        {action && (
          <div className="mt-3">
            <Button size="slim" onClick={action.onClick}>
              {action.label}
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
};

export default StatsCard;