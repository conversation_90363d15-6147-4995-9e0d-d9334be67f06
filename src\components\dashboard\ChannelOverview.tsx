import React from 'react';
import { Card, Text, Badge, ProgressBar } from '@shopify/polaris';

interface ChannelData {
  name: string;
  status: 'connected' | 'disconnected' | 'setup';
  messagesThisMonth: number;
  messageLimit: number;
  color: string;
}

interface ChannelOverviewProps {
  channels: ChannelData[];
  onSetupChannel: (channel: string) => void;
}

const ChannelOverview: React.FC<ChannelOverviewProps> = ({ channels, onSetupChannel }) => {
  const getStatusBadge = (status: ChannelData['status']) => {
    switch (status) {
      case 'connected':
        return <Badge tone="success">Connected</Badge>;
      case 'disconnected':
        return <Badge tone="critical">Disconnected</Badge>;
      case 'setup':
        return <Badge tone="attention">Setup Required</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  const getProgressPercentage = (used: number, limit: number) => {
    return limit > 0 ? Math.round((used / limit) * 100) : 0;
  };

  return (
    <Card>
      <div className="p-4">
        <Text variant="headingMd" as="h3" tone="subdued">
          Channel Status
        </Text>
        
        <div className="mt-4 space-y-4">
          {channels.map((channel) => (
            <div key={channel.name} className="border rounded-lg p-3">
              <div className="flex justify-between items-center mb-2">
                <Text variant="bodyMd" as="span" fontWeight="semibold">
                  {channel.name}
                </Text>
                {getStatusBadge(channel.status)}
              </div>
              
              {channel.status === 'connected' && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <Text variant="bodySm" as="span" tone="subdued">
                      Messages this month
                    </Text>
                    <Text variant="bodySm" as="span">
                      {channel.messagesThisMonth} / {channel.messageLimit}
                    </Text>
                  </div>
                  <ProgressBar 
                    progress={getProgressPercentage(channel.messagesThisMonth, channel.messageLimit)}
                    size="small"
                  />
                </div>
              )}
              
              {channel.status === 'setup' && (
                <div className="mt-2">
                  <button
                    onClick={() => onSetupChannel(channel.name)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Complete setup →
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default ChannelOverview;