import React, { useState } from 'react';
import AppLayout from '../components/layout/AppLayout';
import Dashboard from './Dashboard';
import Notifications from './Notifications';
import Campaigns from './Campaigns';
import Templates from './Templates';
import Settings from './Settings';
import Subscription from './Subscription';

const Index = () => {
  const [currentPath, setCurrentPath] = useState('/');

  const handleNavigate = (path: string) => {
    setCurrentPath(path);
    console.log('Navigating to:', path);
  };

  const renderPage = () => {
    switch (currentPath) {
      case '/':
        return <Dashboard onNavigate={handleNavigate} />;
      case '/notifications':
      case '/notifications/whatsapp':
      case '/notifications/email':
      case '/notifications/sms':
        return <Notifications onNavigate={handleNavigate} />;
      case '/campaigns':
        return <Campaigns onNavigate={handleNavigate} />;
      case '/templates':
        return <Templates onNavigate={handleNavigate} />;
      case '/settings':
        return <Settings onNavigate={handleNavigate} />;
      case '/subscription':
        return <Subscription onNavigate={handleNavigate} />;
      default:
        return <Dashboard onNavigate={handleNavigate} />;
    }
  };

  return (
    <AppLayout currentPath={currentPath} onNavigate={handleNavigate}>
      {renderPage()}
    </AppLayout>
  );
};

export default Index;
