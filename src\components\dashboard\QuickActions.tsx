import React from 'react';
import { Card, Text, ButtonGroup, Button } from '@shopify/polaris';
import { PlusIcon, NotificationIcon, AutomationIcon, ChatIcon } from '@shopify/polaris-icons';

interface QuickActionsProps {
  onAction: (action: string) => void;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onAction }) => {
  const actions = [
    {
      id: 'new-campaign',
      label: 'Create Campaign',
      icon: AutomationIcon,
      variant: 'primary' as const,
    },
    {
      id: 'send-notification',
      label: 'Send Notification',
      icon: NotificationIcon,
      variant: 'secondary' as const,
    },
    {
      id: 'new-template',
      label: 'New Template',
      icon: ChatIcon,
      variant: 'secondary' as const,
    },
  ];

  return (
    <Card>
      <div className="p-4">
        <Text variant="headingMd" as="h3" tone="subdued">
          Quick Actions
        </Text>
        
        <div className="mt-4 space-y-3">
          {actions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant}
              icon={action.icon}
              onClick={() => onAction(action.id)}
              size="large"
              fullWidth
            >
              {action.label}
            </Button>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default QuickActions;