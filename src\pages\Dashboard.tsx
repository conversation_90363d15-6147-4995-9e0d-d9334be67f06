import React from 'react';
import { Page, Layout, Card, Text, Button } from '@shopify/polaris';
import StatsCard from '../components/dashboard/StatsCard';
import QuickActions from '../components/dashboard/QuickActions';
import ChannelOverview from '../components/dashboard/ChannelOverview';

interface DashboardProps {
  onNavigate: (path: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onNavigate }) => {
  const statsData = [
    {
      title: 'Messages Sent This Month',
      value: '1,247',
      trend: { direction: 'up' as const, percentage: 12 },
      action: { label: 'View Details', onClick: () => onNavigate('/notifications') }
    },
    {
      title: 'Active Campaigns',
      value: '8',
      trend: { direction: 'up' as const, percentage: 25 },
      action: { label: 'Manage', onClick: () => onNavigate('/campaigns') }
    },
    {
      title: 'Templates Created',
      value: '24',
      action: { label: 'View All', onClick: () => onNavigate('/templates') }
    },
    {
      title: 'Current Plan',
      value: 'Basic',
      action: { label: 'Upgrade', onClick: () => onNavigate('/subscription') }
    }
  ];

  const channelData = [
    {
      name: 'WhatsApp',
      status: 'connected' as const,
      messagesThisMonth: 847,
      messageLimit: 3000,
      color: '#25D366'
    },
    {
      name: 'Email',
      status: 'connected' as const,
      messagesThisMonth: 320,
      messageLimit: 3000,
      color: '#4285F4'
    },
    {
      name: 'SMS',
      status: 'setup' as const,
      messagesThisMonth: 0,
      messageLimit: 3000,
      color: '#8B5CF6'
    }
  ];

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'new-campaign':
        onNavigate('/campaigns');
        break;
      case 'send-notification':
        onNavigate('/notifications');
        break;
      case 'new-template':
        onNavigate('/templates');
        break;
    }
  };

  const handleSetupChannel = (channel: string) => {
    onNavigate(`/notifications/${channel.toLowerCase()}`);
  };

  return (
    <Page 
      title="Dashboard"
      subtitle="Welcome to Lovable - Post-Purchase Engagement Platform"
    >
      <Layout>
        {/* Stats Overview */}
        <Layout.Section>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {statsData.map((stat, index) => (
              <StatsCard
                key={index}
                title={stat.title}
                value={stat.value}
                trend={stat.trend}
                action={stat.action}
              />
            ))}
          </div>
        </Layout.Section>

        {/* Main Content */}
        <Layout.Section>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <QuickActions onAction={handleQuickAction} />
            <ChannelOverview 
              channels={channelData} 
              onSetupChannel={handleSetupChannel}
            />
          </div>
        </Layout.Section>

        {/* Recent Activity */}
        <Layout.Section>
          <Card>
            <div className="p-4">
              <Text variant="headingMd" as="h3" tone="subdued">
                Recent Activity
              </Text>
              <div className="mt-4 space-y-3">
                <div className="flex justify-between items-center py-2 border-b">
                  <div>
                    <Text variant="bodyMd" as="p">Campaign "iPhone Case Offer" sent</Text>
                    <Text variant="bodySm" as="p" tone="subdued">2 hours ago • 245 recipients</Text>
                  </div>
                  <Text variant="bodySm" as="span" tone="success">✓ Delivered</Text>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <div>
                    <Text variant="bodyMd" as="p">New WhatsApp template approved</Text>
                    <Text variant="bodySm" as="p" tone="subdued">5 hours ago • "Birthday Discount"</Text>
                  </div>
                  <Text variant="bodySm" as="span" tone="success">✓ Approved</Text>
                </div>
                <div className="flex justify-between items-center py-2">
                  <div>
                    <Text variant="bodyMd" as="p">Email template created</Text>
                    <Text variant="bodySm" as="p" tone="subdued">1 day ago • "Order Follow-up"</Text>
                  </div>
                  <Text variant="bodySm" as="span" tone="subdued">Draft</Text>
                </div>
              </div>
            </div>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
};

export default Dashboard;