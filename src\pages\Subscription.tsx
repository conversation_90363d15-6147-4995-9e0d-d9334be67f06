import React, { useState } from 'react';
import { Page, Layout, Card, Text, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, ProgressBar } from '@shopify/polaris';
import { CheckIcon, XIcon, StarFilledIcon } from '@shopify/polaris-icons';

interface SubscriptionProps {
  onNavigate: (path: string) => void;
}

const Subscription: React.FC<SubscriptionProps> = ({ onNavigate }) => {
  const [currentPlan] = useState('basic');
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const plans = {
    free: {
      name: 'Free',
      price: { monthly: 0, yearly: 0 },
      features: {
        email: { templates: 3, smtp: true, messages: 0 },
        whatsapp: { templates: 3, connect: true, messages: 0 },
        sms: { available: false },
        support: 'Community',
        campaigns: 1
      },
      limits: 'Email SMTP + 3 templates, WhatsApp connect + 3 templates (no sending)',
      popular: false
    },
    basic: {
      name: 'Basic',
      price: { monthly: 15, yearly: 150 },
      features: {
        email: { templates: 10, smtp: true, messages: 500 },
        whatsapp: { templates: 10, connect: true, messages: 500 },
        sms: { templates: 3, messages: 500 },
        support: 'Email Support',
        campaigns: 5
      },
      limits: '500 messages/month across all channels',
      popular: true
    },
    advanced: {
      name: 'Advanced',
      price: { monthly: 49, yearly: 490 },
      features: {
        email: { templates: 30, smtp: true, messages: 3000 },
        whatsapp: { templates: 30, connect: true, messages: 3000 },
        sms: { templates: 10, messages: 3000 },
        support: 'Priority Support',
        campaigns: 20
      },
      limits: '3,000 messages/month + $0.005/message after limit',
      popular: false
    }
  };

  const currentPlanData = plans[currentPlan as keyof typeof plans];
  const usageData = {
    messagesUsed: 247,
    messagesLimit: currentPlanData.features.email.messages,
    templatesUsed: {
      email: 6,
      whatsapp: 4,
      sms: 0
    },
    campaignsUsed: 3
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return limit > 0 ? Math.round((used / limit) * 100) : 0;
  };

  const FeatureCheck: React.FC<{ available: boolean; children: React.ReactNode }> = ({ available, children }) => (
    <div className="flex items-center gap-2">
      {available ? (
        <CheckIcon className="h-4 w-4 text-green-600" />
      ) : (
        <XIcon className="h-4 w-4 text-red-600" />
      )}
      <Text variant="bodySm" as="span" tone={available ? 'success' : 'subdued'}>
        {children}
      </Text>
    </div>
  );

  return (
    <Page title="Subscription" subtitle="Manage your plan and billing">
      <Layout>
        {/* Current Plan Status */}
        <Layout.Section>
          <Card>
            <div className="p-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Text variant="headingLg" as="h2">
                      Current Plan: {currentPlanData.name}
                    </Text>
                    {currentPlan === 'basic' && <Badge tone="info">Popular</Badge>}
                  </div>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    {currentPlanData.limits}
                  </Text>
                </div>
                <div className="text-right">
                  <Text variant="headingLg" as="p">
                    ${currentPlanData.price.monthly}/month
                  </Text>
                  <Text variant="bodySm" as="p" tone="subdued">
                    Billed monthly
                  </Text>
                </div>
              </div>

              {/* Usage Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <Text variant="bodyMd" as="p" fontWeight="semibold">Messages This Month</Text>
                    <Text variant="bodySm" as="span">
                      {usageData.messagesUsed} / {usageData.messagesLimit}
                    </Text>
                  </div>
                  <ProgressBar 
                    progress={getUsagePercentage(usageData.messagesUsed, usageData.messagesLimit)} 
                    size="small" 
                  />
                  <Text variant="bodySm" as="p" tone="subdued" className="mt-1">
                    {usageData.messagesLimit - usageData.messagesUsed} messages remaining
                  </Text>
                </div>

                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold" className="mb-2">Templates Used</Text>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <Text variant="bodySm" as="span">Email:</Text>
                      <Text variant="bodySm" as="span">
                        {usageData.templatesUsed.email} / {currentPlanData.features.email.templates}
                      </Text>
                    </div>
                    <div className="flex justify-between">
                      <Text variant="bodySm" as="span">WhatsApp:</Text>
                      <Text variant="bodySm" as="span">
                        {usageData.templatesUsed.whatsapp} / {currentPlanData.features.whatsapp.templates}
                      </Text>
                    </div>
                    {currentPlan !== 'free' && (
                      <div className="flex justify-between">
                        <Text variant="bodySm" as="span">SMS:</Text>
                        <Text variant="bodySm" as="span">
                          {usageData.templatesUsed.sms} / {currentPlanData.features.sms?.templates || 0}
                        </Text>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </Layout.Section>

        {/* Billing Cycle Toggle */}
        <Layout.Section>
          <div className="flex justify-center mb-6">
            <div className="flex items-center gap-4 p-1 bg-gray-100 rounded-lg">
              <button
                onClick={() => setBillingCycle('monthly')}
                className={`px-4 py-2 rounded ${billingCycle === 'monthly' ? 'bg-white shadow' : ''}`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingCycle('yearly')}
                className={`px-4 py-2 rounded ${billingCycle === 'yearly' ? 'bg-white shadow' : ''}`}
              >
                Yearly (Save 17%)
              </button>
            </div>
          </div>
        </Layout.Section>

        {/* Plans Comparison */}
        <Layout.Section>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(plans).map(([planKey, plan]) => (
              <Card key={planKey}>
                <div className="p-6">
                  <div className="text-center mb-6">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <Text variant="headingMd" as="h3">{plan.name}</Text>
                      {plan.popular && <StarFilledIcon className="h-4 w-4 text-yellow-500" />}
                    </div>
                    
                    <div className="mb-4">
                      <Text variant="headingXl" as="p">
                        ${plan.price[billingCycle]}
                      </Text>
                      <Text variant="bodySm" as="p" tone="subdued">
                        per {billingCycle === 'yearly' ? 'year' : 'month'}
                      </Text>
                    </div>

                    {planKey === currentPlan ? (
                      <Badge tone="success">Current Plan</Badge>
                    ) : (
                      <Button 
                        variant={plan.popular ? 'primary' : 'secondary'}
                        fullWidth
                        disabled={planKey === 'free' && currentPlan !== 'free'}
                      >
                        {planKey === 'free' ? 'Downgrade' : 'Upgrade'}
                      </Button>
                    )}
                  </div>

                  <div className="space-y-3">
                    <Text variant="bodyMd" as="p" fontWeight="semibold">Features:</Text>
                    
                    <FeatureCheck available={plan.features.email.smtp}>
                      {plan.features.email.templates} Email templates
                    </FeatureCheck>
                    
                    <FeatureCheck available={plan.features.whatsapp.connect}>
                      {plan.features.whatsapp.templates} WhatsApp templates
                    </FeatureCheck>
                    
                    <FeatureCheck available={plan.features.sms?.available !== false}>
                      {plan.features.sms?.templates || 0} SMS templates
                      {plan.features.sms?.available === false && ' (Upgrade required)'}
                    </FeatureCheck>
                    
                    <FeatureCheck available={true}>
                      {plan.features.campaigns} Active campaigns
                    </FeatureCheck>
                    
                    <FeatureCheck available={true}>
                      {plan.features.support}
                    </FeatureCheck>

                    {plan.features.email.messages > 0 && (
                      <FeatureCheck available={true}>
                        {plan.features.email.messages.toLocaleString()} messages/month
                      </FeatureCheck>
                    )}
                  </div>

                  <div className="mt-4 p-3 bg-gray-50 rounded">
                    <Text variant="bodySm" as="p" tone="subdued">
                      {plan.limits}
                    </Text>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </Layout.Section>

        {/* Billing Information */}
        <Layout.Section>
          <Card>
            <div className="p-4">
              <Text variant="headingMd" as="h3">Billing Information</Text>
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold" className="mb-2">Payment Method</Text>
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <div className="w-8 h-6 bg-blue-600 rounded flex items-center justify-center">
                      <Text variant="bodySm" as="span" tone="base">💳</Text>
                    </div>
                    <div>
                      <Text variant="bodySm" as="p">•••• •••• •••• 4242</Text>
                      <Text variant="bodySm" as="p" tone="subdued">Expires 12/26</Text>
                    </div>
                  </div>
                </div>

                <div>
                  <Text variant="bodyMd" as="p" fontWeight="semibold" className="mb-2">Next Billing Date</Text>
                  <Text variant="bodyMd" as="p">January 15, 2025</Text>
                  <Text variant="bodySm" as="p" tone="subdued">
                    Amount: ${currentPlanData.price.monthly}
                  </Text>
                </div>
              </div>

              <div className="mt-6 flex gap-3">
                <Button>Update Payment Method</Button>
                <Button variant="plain">Download Invoices</Button>
                <Button variant="plain" tone="critical">Cancel Subscription</Button>
              </div>
            </div>
          </Card>
        </Layout.Section>

        {/* Feature Limits Notice */}
        {currentPlan === 'free' && (
          <Layout.Section>
            <Banner tone="info">
              <p>
                You're on the Free plan. Upgrade to Basic or Advanced to unlock SMS messaging, 
                increase message limits, and get priority support.
              </p>
            </Banner>
          </Layout.Section>
        )}
      </Layout>
    </Page>
  );
};

export default Subscription;