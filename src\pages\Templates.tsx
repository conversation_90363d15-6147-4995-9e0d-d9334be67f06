import React, { useState } from 'react';
import { <PERSON>, Layout, Card, Text, Button, Tabs, Badge, Modal, TextField, Select, TextContainer } from '@shopify/polaris';
import { PlusIcon, EmailIcon, ChatIcon, PhoneIcon, ViewIcon, EditIcon, DeleteIcon } from '@shopify/polaris-icons';

interface TemplatesProps {
  onNavigate: (path: string) => void;
}

const Templates: React.FC<TemplatesProps> = ({ onNavigate }) => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [modalActive, setModalActive] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [templateType, setTemplateType] = useState('whatsapp');
  const [previewModal, setPreviewModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  const tabs = [
    { id: 'all', content: 'All Templates' },
    { id: 'whatsapp', content: 'WhatsApp' },
    { id: 'email', content: 'Email' },
    { id: 'sms', content: 'SMS' },
  ];

  const templates = [
    {
      id: 1,
      name: 'Order Confirmation',
      type: 'email',
      status: 'approved',
      subject: 'Your order has been confirmed!',
      preview: 'Hi {{customer_name}}, Thank you for your order #{{order_number}}...',
      lastModified: '2 days ago',
      timesUsed: 156
    },
    {
      id: 2,
      name: 'iPhone Case Offer',
      type: 'whatsapp',
      status: 'approved',
      subject: '',
      preview: 'Hi {{customer_name}}! We noticed you bought an iPhone. Check out our premium cases...',
      lastModified: '1 week ago',
      timesUsed: 89
    },
    {
      id: 3,
      name: 'Birthday Discount',
      type: 'whatsapp',
      status: 'pending',
      subject: '',
      preview: 'Happy Birthday {{customer_name}}! Enjoy 20% off your next purchase...',
      lastModified: '3 days ago',
      timesUsed: 23
    },
    {
      id: 4,
      name: 'Shipping Update',
      type: 'email',
      status: 'approved',
      subject: 'Your order is on the way!',
      preview: 'Great news! Your order #{{order_number}} has shipped...',
      lastModified: '5 days ago',
      timesUsed: 234
    },
    {
      id: 5,
      name: 'Cart Reminder',
      type: 'sms',
      status: 'draft',
      subject: '',
      preview: 'Hey {{customer_name}}, you left something in your cart...',
      lastModified: '1 day ago',
      timesUsed: 0
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge tone="success">Approved</Badge>;
      case 'pending':
        return <Badge tone="attention">Pending Review</Badge>;
      case 'rejected':
        return <Badge tone="critical">Rejected</Badge>;
      case 'draft':
        return <Badge>Draft</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'whatsapp':
        return <ChatIcon className="h-4 w-4" />;
      case 'email':
        return <EmailIcon className="h-4 w-4" />;
      case 'sms':
        return <PhoneIcon className="h-4 w-4" />;
      default:
        return <ChatIcon className="h-4 w-4" />;
    }
  };

  const filteredTemplates = selectedTab === 0 
    ? templates 
    : templates.filter(template => template.type === tabs[selectedTab].id);

  const handleCreateTemplate = () => {
    console.log('Creating template:', { templateName, templateType });
    setModalActive(false);
    setTemplateName('');
    setTemplateType('whatsapp');
  };

  const handlePreview = (template: any) => {
    setSelectedTemplate(template);
    setPreviewModal(true);
  };

  const templateTypeOptions = [
    { label: 'WhatsApp', value: 'whatsapp' },
    { label: 'Email', value: 'email' },
    { label: 'SMS', value: 'sms' },
  ];

  return (
    <Page 
      title="Templates" 
      subtitle="Create and manage message templates for all channels"
      primaryAction={{
        content: 'Create Template',
        icon: PlusIcon,
        onAction: () => setModalActive(true)
      }}
    >
      <Layout>
        {/* Template Stats */}
        <Layout.Section>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <div className="p-4">
                <Text variant="headingMd" as="h3">Total Templates</Text>
                <Text variant="headingXl" as="p">24</Text>
                <Text variant="bodySm" as="p" tone="subdued">5 created this month</Text>
              </div>
            </Card>
            
            <Card>
              <div className="p-4">
                <Text variant="headingMd" as="h3">WhatsApp Templates</Text>
                <Text variant="headingXl" as="p">12</Text>
                <Text variant="bodySm" as="p" tone="success">8 approved</Text>
              </div>
            </Card>
            
            <Card>
              <div className="p-4">
                <Text variant="headingMd" as="h3">Email Templates</Text>
                <Text variant="headingXl" as="p">10</Text>
                <Text variant="bodySm" as="p" tone="success">All active</Text>
              </div>
            </Card>
            
            <Card>
              <div className="p-4">
                <Text variant="headingMd" as="h3">SMS Templates</Text>
                <Text variant="headingXl" as="p">2</Text>
                <Text variant="bodySm" as="p" tone="attention">Upgrade to create more</Text>
              </div>
            </Card>
          </div>
        </Layout.Section>

        {/* Templates List */}
        <Layout.Section>
          <Card>
            <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
              <div className="p-4">
                <div className="space-y-4">
                  {filteredTemplates.map((template) => (
                    <div key={template.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            {getTypeIcon(template.type)}
                            <Text variant="bodyLg" as="h4" fontWeight="semibold">
                              {template.name}
                            </Text>
                            {getStatusBadge(template.status)}
                            <Badge tone="info">{template.type.toUpperCase()}</Badge>
                          </div>
                          
                          {template.subject && (
                            <Text variant="bodySm" as="p" tone="subdued" fontWeight="medium">
                              Subject: {template.subject}
                            </Text>
                          )}
                          
                          <Text variant="bodySm" as="p" tone="subdued" className="mt-1 line-clamp-2">
                            {template.preview}
                          </Text>
                          
                          <div className="flex items-center gap-4 mt-3 text-xs text-gray-500">
                            <span>Modified {template.lastModified}</span>
                            <span>•</span>
                            <span>Used {template.timesUsed} times</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Button size="slim" icon={ViewIcon} onClick={() => handlePreview(template)}>
                            Preview
                          </Button>
                          <Button size="slim" icon={EditIcon}>
                            Edit
                          </Button>
                          <Button size="slim" icon={DeleteIcon} variant="plain" tone="critical">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {filteredTemplates.length === 0 && (
                    <div className="text-center py-8">
                      <Text variant="bodyMd" as="p" tone="subdued">
                        No templates found for this channel. Create your first template to get started.
                      </Text>
                      <div className="mt-4">
                        <Button onClick={() => setModalActive(true)}>
                          Create Template
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Tabs>
          </Card>
        </Layout.Section>

        {/* Template Guidelines */}
        <Layout.Section>
          <Card>
            <div className="p-4">
              <Text variant="headingMd" as="h3">Template Guidelines</Text>
              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <ChatIcon className="h-4 w-4" />
                    <Text variant="bodyMd" as="p" fontWeight="semibold">WhatsApp</Text>
                  </div>
                  <Text variant="bodySm" as="p" tone="subdued">
                    Templates must be approved by Meta. Include clear business purpose and avoid promotional language in headers.
                  </Text>
                </div>
                
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <EmailIcon className="h-4 w-4" />
                    <Text variant="bodyMd" as="p" fontWeight="semibold">Email</Text>
                  </div>
                  <Text variant="bodySm" as="p" tone="subdued">
                    Include unsubscribe links and sender information. Use responsive design for mobile compatibility.
                  </Text>
                </div>
                
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <PhoneIcon className="h-4 w-4" />
                    <Text variant="bodyMd" as="p" fontWeight="semibold">SMS</Text>
                  </div>
                  <Text variant="bodySm" as="p" tone="subdued">
                    Keep messages under 160 characters. Include opt-out instructions and sender ID.
                  </Text>
                </div>
              </div>
            </div>
          </Card>
        </Layout.Section>
      </Layout>

      {/* Create Template Modal */}
      <Modal
        open={modalActive}
        onClose={() => setModalActive(false)}
        title="Create New Template"
        primaryAction={{
          content: 'Create Template',
          onAction: handleCreateTemplate,
          disabled: !templateName.trim()
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => setModalActive(false)
          }
        ]}
      >
        <Modal.Section>
          <div className="space-y-4">
            <TextField
              label="Template Name"
              value={templateName}
              onChange={setTemplateName}
              placeholder="e.g., Order Confirmation"
              autoComplete="off"
            />
            
            <Select
              label="Channel"
              options={templateTypeOptions}
              value={templateType}
              onChange={setTemplateType}
            />
            
            <div className="p-4 bg-blue-50 rounded">
              <Text variant="bodySm" as="p">
                💡 <strong>Next Step:</strong> After creating the template, you'll be able to customize the message content, 
                add variables, and submit for approval if required.
              </Text>
            </div>
          </div>
        </Modal.Section>
      </Modal>

      {/* Preview Modal */}
      <Modal
        open={previewModal}
        onClose={() => setPreviewModal(false)}
        title={`Preview: ${selectedTemplate?.name}`}
        secondaryActions={[
          {
            content: 'Close',
            onAction: () => setPreviewModal(false)
          }
        ]}
      >
        <Modal.Section>
          {selectedTemplate && (
            <div>
              <div className="mb-4">
                <Text variant="bodyMd" as="p" fontWeight="semibold">
                  Channel: {selectedTemplate.type.toUpperCase()}
                </Text>
                {selectedTemplate.subject && (
                  <Text variant="bodySm" as="p" tone="subdued">
                    Subject: {selectedTemplate.subject}
                  </Text>
                )}
              </div>
              
              <div className="p-4 border rounded-lg bg-gray-50">
                <TextContainer>
                  <Text variant="bodyMd" as="p">
                    {selectedTemplate.preview}
                  </Text>
                </TextContainer>
              </div>
              
              <div className="mt-4">
                <Text variant="bodySm" as="p" tone="subdued">
                  Variables like {{customer_name}} and {{order_number}} will be automatically replaced with actual values when sent.
                </Text>
              </div>
            </div>
          )}
        </Modal.Section>
      </Modal>
    </Page>
  );
};

export default Templates;